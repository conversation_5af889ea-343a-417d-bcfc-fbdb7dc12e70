<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-bold text-gray-900">走失宠物协寻平台</h1>
          </div>
          
          <div class="flex items-center space-x-4">
            <template v-if="authStore.isAuthenticated">
              <span class="text-gray-700">欢迎，{{ authStore.user?.username }}</span>
              <button
                @click="handleLogout"
                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                登出
              </button>
            </template>
            <template v-else>
              <router-link
                to="/login"
                class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
              >
                登录
              </router-link>
              <router-link
                to="/register"
                class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                注册
              </router-link>
            </template>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <div class="text-center">
          <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
            帮助走失的宠物回家
          </h2>
          <p class="mt-4 text-lg text-gray-600">
            快速发布走失信息，让更多人帮助寻找您的宠物
          </p>
          
          <div class="mt-8 flex justify-center space-x-4">
            <router-link
              to="/post/create"
              class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md text-lg font-medium"
            >
              发布走失信息
            </router-link>
            <router-link
              to="/posts"
              class="bg-gray-200 hover:bg-gray-300 text-gray-900 px-6 py-3 rounded-md text-lg font-medium"
            >
              浏览寻宠信息
            </router-link>
          </div>
        </div>
        
        <!-- 功能介绍 -->
        <div class="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          <div class="text-center">
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-md bg-primary-500 text-white">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <h3 class="mt-4 text-lg font-medium text-gray-900">一键通报</h3>
            <p class="mt-2 text-base text-gray-500">
              快速填写宠物信息，2分钟内完成走失通报
            </p>
          </div>
          
          <div class="text-center">
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-md bg-primary-500 text-white">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <h3 class="mt-4 text-lg font-medium text-gray-900">智能搜索</h3>
            <p class="mt-2 text-base text-gray-500">
              多维度筛选，快速找到相关的走失信息
            </p>
          </div>
          
          <div class="text-center">
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-md bg-primary-500 text-white">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
            </div>
            <h3 class="mt-4 text-lg font-medium text-gray-900">社交分享</h3>
            <p class="mt-2 text-base text-gray-500">
              一键分享到各大社交平台，扩大寻找范围
            </p>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 处理登出
const handleLogout = async () => {
  await authStore.logout()
  router.push('/')
}
</script>
